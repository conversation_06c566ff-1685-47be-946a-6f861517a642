// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function AddRemote(arg1, arg2, arg3) {
  return window['go']['backend']['App']['AddRemote'](arg1, arg2, arg3);
}

export function DeleteRemote(arg1) {
  return window['go']['backend']['App']['DeleteRemote'](arg1);
}

export function GetConfigInfo() {
  return window['go']['backend']['App']['GetConfigInfo']();
}

export function GetRemotes() {
  return window['go']['backend']['App']['GetRemotes']();
}

export function StopAddingRemote() {
  return window['go']['backend']['App']['StopAddingRemote']();
}

export function StopCommand(arg1) {
  return window['go']['backend']['App']['StopCommand'](arg1);
}

export function Sync(arg1, arg2) {
  return window['go']['backend']['App']['Sync'](arg1, arg2);
}

export function SyncWithTab(arg1, arg2, arg3) {
  return window['go']['backend']['App']['SyncWithTab'](arg1, arg2, arg3);
}

export function SyncWithTabId(arg1, arg2, arg3) {
  return window['go']['backend']['App']['SyncWithTabId'](arg1, arg2, arg3);
}

export function UpdateProfiles(arg1) {
  return window['go']['backend']['App']['UpdateProfiles'](arg1);
}

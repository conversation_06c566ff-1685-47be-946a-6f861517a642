version: "3"

tasks:
  # Desktop
  dev:
    cmds:
      - rm -rf ./frontend/dist
      - wails dev
    dir: desktop

  build-mac:
    platforms: [darwin]
    cmds:
      - rm -rf ./frontend/dist
      - wails build -v 2
      - rm -rf ../desktop.app
      - mv build/bin/desktop.app ../desktop.app
      # - codesign --sign - --force --deep build/bin/desktop.app
      # - upx --best build/bin/desktop --force-macos
      # - chmod +x build/bin/desktop.app/Contents/MacOS/desktop
      - open ../desktop.app
    dir: desktop

  build-win:
    platforms: [windows]
    cmds:
      - wails build -v 2 # -windowsconsole to show the console for debugging
      - powershell.exe "Move-Item -Path ./build/bin/desktop.exe -Destination ../desktop.exe -Force"
    dir: desktop
